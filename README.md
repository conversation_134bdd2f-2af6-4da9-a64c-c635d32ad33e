# 智能管理系统

一个现代化的智能管理系统，包含手机验证登录和多功能主控台界面。

## 📁 项目结构

```
智能管理系统/
├── index.html              # 主入口页面
├── pages/                  # 页面文件夹
│   ├── login.html          # 登录页面
│   └── dashboard.html      # 主控台页面
├── assets/                 # 资源文件夹
│   ├── css/               # 样式文件
│   │   ├── login.css      # 登录页面样式
│   │   └── dashboard.css  # 主控台样式
│   └── js/                # JavaScript文件
│       ├── login.js       # 登录页面逻辑
│       └── dashboard.js   # 主控台逻辑
└── README.md              # 项目说明文档
```

## 🚀 功能特性

### 🔐 登录系统
- **手机号验证登录**：支持手机号+短信验证码登录
- **实时验证**：手机号格式验证、验证码格式验证
- **倒计时功能**：获取验证码后60秒倒计时
- **响应式设计**：完美适配桌面端和移动端
- **优雅动画**：登录成功提示动画

### 📊 主控台系统
- **侧边导航栏**：可折叠的侧边导航，支持移动端
- **数据大屏**：实时数据展示，包含统计卡片和图表
- **题库管理**：完整的题目管理功能
- **多页面切换**：单页面应用，流畅的页面切换
- **响应式布局**：适配各种屏幕尺寸

## 🎨 界面设计

### 数据大屏界面
- **统计卡片**：用户数、题库数、访问量、完成率等关键指标
- **趋势图表**：用户增长趋势线图
- **分布图表**：题目分类分布饼图
- **活动列表**：最近系统活动记录
- **实时更新**：数据实时刷新显示

### 题库管理界面
- **筛选功能**：按题目类型、学科、难度筛选
- **搜索功能**：题目内容关键词搜索
- **表格展示**：清晰的表格布局展示题目信息
- **操作按钮**：预览、编辑、删除等操作
- **分页功能**：支持大量数据分页显示

## 🛠️ 技术栈

- **前端框架**：原生 HTML5 + CSS3 + JavaScript
- **图表库**：Chart.js
- **图标库**：Font Awesome 6.0
- **响应式**：CSS Grid + Flexbox
- **动画效果**：CSS Transitions + Transforms

## 📱 响应式支持

- **桌面端**：≥1024px，完整功能展示
- **平板端**：768px-1023px，适配中等屏幕
- **移动端**：≤767px，移动优化布局

## 🎯 设计亮点

### 视觉设计
- **现代化UI**：采用卡片式设计，圆角阴影
- **渐变配色**：紫蓝色渐变主题，视觉层次丰富
- **图标系统**：Font Awesome图标，语义化清晰
- **动画交互**：悬停效果、过渡动画提升用户体验

### 用户体验
- **直观导航**：清晰的侧边导航和面包屑
- **即时反馈**：表单验证、操作确认、状态提示
- **快速操作**：键盘快捷键、批量操作支持
- **无障碍设计**：语义化HTML、键盘导航支持

## 🚀 快速开始

1. **启动服务器**
   ```bash
   python3 -m http.server 8000
   ```

2. **访问应用**
   ```
   http://localhost:8000
   ```

3. **登录测试**
   - 输入任意11位手机号
   - 点击获取验证码
   - 输入任意6位数字验证码
   - 点击登录进入主控台

## 📋 待开发功能

- [ ] 用户管理模块
- [ ] 数据分析模块  
- [ ] 系统设置模块
- [ ] 题目编辑器
- [ ] 批量导入导出
- [ ] 权限管理系统
- [ ] 实时通知系统

## 🔧 自定义配置

### 主题颜色
可以在 `assets/css/dashboard.css` 中修改CSS变量来自定义主题：

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}
```

### 侧边栏菜单
在 `pages/dashboard.html` 中的 `.sidebar-menu` 部分添加新的菜单项。

## 📞 技术支持

如有问题或建议，请联系开发团队。
