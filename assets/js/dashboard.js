// 主控台JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initializeDashboard();
    initializeCharts();
    loadQuestionsData();
});

// 初始化主控台
function initializeDashboard() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const menuItems = document.querySelectorAll('.menu-item');
    
    // 侧边栏切换
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
    });
    
    // 移动端菜单切换
    mobileMenuToggle.addEventListener('click', function() {
        sidebar.classList.toggle('mobile-open');
    });
    
    // 菜单项点击事件
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有活动状态
            menuItems.forEach(mi => mi.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            
            // 获取页面标识
            const pageId = this.getAttribute('data-page');
            
            // 切换页面
            switchPage(pageId);
        });
    });
    
    // 点击主内容区域时关闭移动端菜单
    document.getElementById('mainContent').addEventListener('click', function() {
        if (window.innerWidth <= 1024) {
            sidebar.classList.remove('mobile-open');
        }
    });
}

// 页面切换函数
function switchPage(pageId) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page-content');
    pages.forEach(page => page.classList.remove('active'));
    
    // 显示目标页面
    const targetPage = document.getElementById(pageId + 'Page');
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    // 更新页面标题
    const pageTitle = document.getElementById('pageTitle');
    const titles = {
        'home': '数据大屏',
        'questions': '题库管理',
        'users': '用户管理',
        'analytics': '数据分析',
        'settings': '系统设置'
    };
    
    pageTitle.textContent = titles[pageId] || '未知页面';
}

// 初始化图表
function initializeCharts() {
    // 用户增长趋势图
    const userGrowthCtx = document.getElementById('userGrowthChart');
    if (userGrowthCtx) {
        new Chart(userGrowthCtx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '新增用户',
                    data: [120, 190, 300, 500, 200, 300, 450],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
    
    // 题目分类分布图
    const categoryCtx = document.getElementById('categoryChart');
    if (categoryCtx) {
        new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: ['数学', '英语', '语文', '科学', '其他'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: [
                        '#3498db',
                        '#e74c3c',
                        '#f39c12',
                        '#27ae60',
                        '#9b59b6'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
}

// 模拟题目数据
const mockQuestions = [
    {
        id: 1,
        type: 'single',
        userAvatar: 'https://via.placeholder.com/40x40/3498db/ffffff?text=U1',
        content: '在数学中，质数是指大于1的自然数中，除了1和它本身以外不再有其他因数的自然数。下列选项中哪个数字是质数？请仔细分析每个选项的因数分解情况，并选择正确答案。',
        questionImage: 'https://via.placeholder.com/50x35/e74c3c/ffffff?text=IMG',
        options: ['A. 15', 'B. 17', 'C. 21', 'D. 25'],
        correctAnswer: 'B',
        isVerified: 'verified',
        createTime: '2024-01-15'
    },
    {
        id: 2,
        type: 'single',
        userAvatar: null,
        content: 'France is a country located in Western Europe, known for its rich history, culture, and cuisine. The country has been a major power in European politics for centuries. What is the capital city of France?',
        questionImage: null,
        options: ['A. Lyon', 'B. Marseille', 'C. Paris', 'D. Nice'],
        correctAnswer: 'C',
        isVerified: 'pending',
        createTime: '2024-01-14'
    },
    {
        id: 3,
        type: 'judge',
        userAvatar: 'https://via.placeholder.com/40x40/27ae60/ffffff?text=U3',
        content: '地球是太阳系中的第三颗行星，也是目前已知唯一存在生命的天体。关于地球的形状，经过长期的科学研究和观测，人类已经得出了明确的结论。请判断以下说法是否正确：地球是一个完美的球体形状。',
        questionImage: 'https://via.placeholder.com/50x35/27ae60/ffffff?text=地球',
        options: ['正确', '错误'],
        correctAnswer: '错误',
        isVerified: 'verified',
        createTime: '2024-01-13'
    },
    {
        id: 4,
        type: 'fill',
        userAvatar: 'https://via.placeholder.com/40x40/f39c12/ffffff?text=U4',
        content: '中华人民共和国成立于1949年10月1日，是世界上人口最多的国家之一。中国有着悠久的历史和灿烂的文化，拥有众多的历史名城和文化遗产。请填空：中华人民共和国的首都是________。',
        questionImage: null,
        options: ['填空题：首都名称'],
        correctAnswer: '北京',
        isVerified: 'rejected',
        createTime: '2024-01-12'
    },
    {
        id: 5,
        type: 'multiple',
        userAvatar: null,
        content: '编程语言是用来编写计算机程序的形式化语言，它们具有特定的语法规则和语义。随着计算机技术的发展，出现了许多不同类型的编程语言。以下选项中哪些是编程语言？',
        questionImage: 'https://via.placeholder.com/50x35/9b59b6/ffffff?text=CODE',
        options: ['A. Python', 'B. Photoshop', 'C. JavaScript', 'D. Microsoft Word', 'E. Java', 'F. Excel'],
        correctAnswer: 'A,C,E',
        isVerified: 'verified',
        createTime: '2024-01-11'
    },
    {
        id: 6,
        type: 'single',
        userAvatar: 'https://via.placeholder.com/40x40/e67e22/ffffff?text=U6',
        content: '物理学是研究物质运动规律和物质基本结构的学科。在经典力学中，牛顿三大定律是基础理论。请根据牛顿第二定律F=ma，计算以下问题：一个质量为2kg的物体，在10N的恒定外力作用下，其加速度是多少？',
        questionImage: 'https://via.placeholder.com/50x35/34495e/ffffff?text=物理',
        options: ['A. 5m/s²', 'B. 20m/s²', 'C. 0.2m/s²', 'D. 12m/s²'],
        correctAnswer: 'A',
        isVerified: 'pending',
        createTime: '2024-01-10'
    },
    {
        id: 7,
        type: 'fill',
        userAvatar: 'https://via.placeholder.com/40x40/8e44ad/ffffff?text=U7',
        content: '古代中国文学作品中，《红楼梦》是清代作家曹雪芹创作的长篇小说，被誉为中国古典小说的巅峰之作。小说以贾宝玉、林黛玉、薛宝钗的爱情悲剧为主线，描写了贾、史、王、薛四大家族的兴衰史。请填空：《红楼梦》的作者是________。',
        questionImage: null,
        options: ['填空题：作者姓名'],
        correctAnswer: '曹雪芹',
        isVerified: 'verified',
        createTime: '2024-01-09'
    },
    {
        id: 8,
        type: 'single',
        userAvatar: null,
        content: 'In English grammar, there are different types of sentences based on their structure and purpose. Understanding sentence types is crucial for effective communication. Which of the following sentences is an example of a complex sentence?',
        questionImage: 'https://via.placeholder.com/50x35/2c3e50/ffffff?text=ENG',
        options: ['A. I like coffee.', 'B. I like coffee, and she likes tea.', 'C. Although it was raining, we went for a walk.', 'D. Stop!'],
        correctAnswer: 'C',
        isVerified: 'pending',
        createTime: '2024-01-08'
    }
];

// 加载题目数据
function loadQuestionsData() {
    const tableBody = document.getElementById('questionsTableBody');
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    mockQuestions.forEach(question => {
        const row = createQuestionRow(question);
        tableBody.appendChild(row);
    });
}

// 创建题目行
function createQuestionRow(question) {
    const row = document.createElement('tr');

    // 类型映射
    const typeMap = {
        'single': { text: '单选题', class: 'type-single' },
        'multiple': { text: '多选题', class: 'type-multiple' },
        'judge': { text: '判断题', class: 'type-judge' },
        'fill': { text: '填空题', class: 'type-fill' }
    };

    // 验证状态映射
    const verificationMap = {
        'verified': { text: '已验证', class: 'verification-verified' },
        'pending': { text: '待验证', class: 'verification-pending' },
        'rejected': { text: '已拒绝', class: 'verification-rejected' }
    };

    // 用户头像HTML
    const userAvatarHtml = question.userAvatar
        ? `<img src="${question.userAvatar}" alt="用户头像" class="user-avatar-small" onclick="viewUserAvatar('${question.userAvatar}')">`
        : `<div class="no-avatar">无头像</div>`;

    // 题干图片HTML
    const questionImageHtml = question.questionImage
        ? `<img src="${question.questionImage}" alt="题干图片" class="question-image-small" onclick="viewQuestionImage('${question.questionImage}')">`
        : `<div class="no-image">无图片</div>`;

    // 选项HTML
    const optionsHtml = question.options.map(option =>
        `<div class="option-item">${option}</div>`
    ).join('');

    row.innerHTML = `
        <td>${question.id}</td>
        <td>
            <span class="type-badge ${typeMap[question.type].class}">
                ${typeMap[question.type].text}
            </span>
        </td>
        <td class="user-avatar-cell">
            ${userAvatarHtml}
        </td>
        <td>
            <div class="question-content" title="${question.content}">
                ${question.content}
            </div>
        </td>
        <td class="question-image-cell">
            ${questionImageHtml}
        </td>
        <td>
            <div class="options-content" title="点击查看完整选项">
                ${optionsHtml}
            </div>
        </td>
        <td>
            <div class="correct-answer">
                ${question.correctAnswer}
            </div>
        </td>
        <td>
            <span class="verification-badge ${verificationMap[question.isVerified].class}">
                ${verificationMap[question.isVerified].text}
            </span>
        </td>
        <td>${question.createTime}</td>
        <td>
            <div class="action-buttons">
                <button class="action-btn-small btn-preview" onclick="previewQuestion(${question.id})">
                    预览
                </button>
                <button class="action-btn-small btn-edit" onclick="editQuestion(${question.id})">
                    编辑
                </button>
                <button class="action-btn-small btn-delete" onclick="deleteQuestion(${question.id})">
                    删除
                </button>
            </div>
        </td>
    `;

    return row;
}

// 题目操作函数
function addQuestion() {
    alert('添加题目功能正在开发中...');
}

function previewQuestion(id) {
    alert(`预览题目 ID: ${id}`);
}

function editQuestion(id) {
    alert(`编辑题目 ID: ${id}`);
}

function deleteQuestion(id) {
    if (confirm('确定要删除这道题目吗？')) {
        alert(`删除题目 ID: ${id}`);
        // 这里可以添加实际的删除逻辑
    }
}

// 查看用户头像
function viewUserAvatar(avatarUrl) {
    // 创建模态框显示大图
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        cursor: pointer;
    `;

    const img = document.createElement('img');
    img.src = avatarUrl;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    `;

    modal.appendChild(img);
    document.body.appendChild(modal);

    modal.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
}

// 查看题干图片
function viewQuestionImage(imageUrl) {
    // 创建模态框显示大图
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        cursor: pointer;
    `;

    const img = document.createElement('img');
    img.src = imageUrl;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.5);
    `;

    modal.appendChild(img);
    document.body.appendChild(modal);

    modal.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        window.location.href = './login.html';
    }
}

// 窗口大小改变时的处理
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');
    if (window.innerWidth > 1024) {
        sidebar.classList.remove('mobile-open');
    }
});
