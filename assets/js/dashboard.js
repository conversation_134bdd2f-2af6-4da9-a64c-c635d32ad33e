// 主控台JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initializeDashboard();
    initializeCharts();
    loadQuestionsData();
});

// 初始化主控台
function initializeDashboard() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const menuItems = document.querySelectorAll('.menu-item');
    
    // 侧边栏切换
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
    });
    
    // 移动端菜单切换
    mobileMenuToggle.addEventListener('click', function() {
        sidebar.classList.toggle('mobile-open');
    });
    
    // 菜单项点击事件
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有活动状态
            menuItems.forEach(mi => mi.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            
            // 获取页面标识
            const pageId = this.getAttribute('data-page');
            
            // 切换页面
            switchPage(pageId);
        });
    });
    
    // 点击主内容区域时关闭移动端菜单
    document.getElementById('mainContent').addEventListener('click', function() {
        if (window.innerWidth <= 1024) {
            sidebar.classList.remove('mobile-open');
        }
    });
}

// 页面切换函数
function switchPage(pageId) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page-content');
    pages.forEach(page => page.classList.remove('active'));
    
    // 显示目标页面
    const targetPage = document.getElementById(pageId + 'Page');
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    // 更新页面标题
    const pageTitle = document.getElementById('pageTitle');
    const titles = {
        'home': '数据大屏',
        'questions': '题库管理',
        'users': '用户管理',
        'analytics': '数据分析',
        'settings': '系统设置'
    };
    
    pageTitle.textContent = titles[pageId] || '未知页面';
}

// 初始化图表
function initializeCharts() {
    // 用户增长趋势图
    const userGrowthCtx = document.getElementById('userGrowthChart');
    if (userGrowthCtx) {
        new Chart(userGrowthCtx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '新增用户',
                    data: [120, 190, 300, 500, 200, 300, 450],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
    
    // 题目分类分布图
    const categoryCtx = document.getElementById('categoryChart');
    if (categoryCtx) {
        new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: ['数学', '英语', '语文', '科学', '其他'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: [
                        '#3498db',
                        '#e74c3c',
                        '#f39c12',
                        '#27ae60',
                        '#9b59b6'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
}

// 模拟题目数据
const mockQuestions = [
    {
        id: 1,
        content: '下列哪个数字是质数？',
        type: 'single',
        subject: 'math',
        difficulty: 'easy',
        createTime: '2024-01-15'
    },
    {
        id: 2,
        content: 'What is the capital of France?',
        type: 'single',
        subject: 'english',
        difficulty: 'medium',
        createTime: '2024-01-14'
    },
    {
        id: 3,
        content: '判断：地球是圆的。',
        type: 'judge',
        subject: 'science',
        difficulty: 'easy',
        createTime: '2024-01-13'
    },
    {
        id: 4,
        content: '请填空：中国的首都是____。',
        type: 'fill',
        subject: 'chinese',
        difficulty: 'easy',
        createTime: '2024-01-12'
    },
    {
        id: 5,
        content: '以下哪些是编程语言？（多选）',
        type: 'multiple',
        subject: 'science',
        difficulty: 'hard',
        createTime: '2024-01-11'
    }
];

// 加载题目数据
function loadQuestionsData() {
    const tableBody = document.getElementById('questionsTableBody');
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    mockQuestions.forEach(question => {
        const row = createQuestionRow(question);
        tableBody.appendChild(row);
    });
}

// 创建题目行
function createQuestionRow(question) {
    const row = document.createElement('tr');
    
    // 类型映射
    const typeMap = {
        'single': { text: '单选题', class: 'type-single' },
        'multiple': { text: '多选题', class: 'type-multiple' },
        'judge': { text: '判断题', class: 'type-judge' },
        'fill': { text: '填空题', class: 'type-fill' }
    };
    
    // 学科映射
    const subjectMap = {
        'math': '数学',
        'english': '英语',
        'chinese': '语文',
        'science': '科学'
    };
    
    // 难度映射
    const difficultyMap = {
        'easy': { text: '简单', class: 'difficulty-easy' },
        'medium': { text: '中等', class: 'difficulty-medium' },
        'hard': { text: '困难', class: 'difficulty-hard' }
    };
    
    row.innerHTML = `
        <td>${question.id}</td>
        <td>
            <div class="question-content" title="${question.content}">
                ${question.content}
            </div>
        </td>
        <td>
            <span class="type-badge ${typeMap[question.type].class}">
                ${typeMap[question.type].text}
            </span>
        </td>
        <td>${subjectMap[question.subject]}</td>
        <td>
            <span class="difficulty-badge ${difficultyMap[question.difficulty].class}">
                ${difficultyMap[question.difficulty].text}
            </span>
        </td>
        <td>${question.createTime}</td>
        <td>
            <div class="action-buttons">
                <button class="action-btn-small btn-preview" onclick="previewQuestion(${question.id})">
                    预览
                </button>
                <button class="action-btn-small btn-edit" onclick="editQuestion(${question.id})">
                    编辑
                </button>
                <button class="action-btn-small btn-delete" onclick="deleteQuestion(${question.id})">
                    删除
                </button>
            </div>
        </td>
    `;
    
    return row;
}

// 题目操作函数
function addQuestion() {
    alert('添加题目功能正在开发中...');
}

function previewQuestion(id) {
    alert(`预览题目 ID: ${id}`);
}

function editQuestion(id) {
    alert(`编辑题目 ID: ${id}`);
}

function deleteQuestion(id) {
    if (confirm('确定要删除这道题目吗？')) {
        alert(`删除题目 ID: ${id}`);
        // 这里可以添加实际的删除逻辑
    }
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        window.location.href = './login.html';
    }
}

// 窗口大小改变时的处理
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');
    if (window.innerWidth > 1024) {
        sidebar.classList.remove('mobile-open');
    }
});
