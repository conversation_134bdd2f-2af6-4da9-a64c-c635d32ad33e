/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    color: #333;
    overflow-x: hidden;
}

/* 侧边导航栏样式 */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo i {
    font-size: 28px;
    color: #3498db;
}

.logo-text {
    font-size: 20px;
    font-weight: 700;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .logo-text {
    opacity: 0;
    width: 0;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* 侧边菜单样式 */
.sidebar-menu {
    list-style: none;
    padding: 20px 0;
    flex: 1;
}

.menu-item {
    margin-bottom: 5px;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.menu-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.menu-item.active .menu-link {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
    border-right: 3px solid #3498db;
}

.menu-link i {
    font-size: 18px;
    width: 24px;
    margin-right: 15px;
}

.menu-text {
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .menu-text {
    opacity: 0;
    width: 0;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-details {
    display: flex;
    flex-direction: column;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .user-details {
    opacity: 0;
    width: 0;
}

.user-name {
    font-weight: 600;
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.logout-btn {
    width: 100%;
    background: rgba(231, 76, 60, 0.2);
    border: 1px solid rgba(231, 76, 60, 0.3);
    color: #e74c3c;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.logout-btn:hover {
    background: rgba(231, 76, 60, 0.3);
}

.sidebar.collapsed .logout-btn span {
    display: none;
}

/* 主内容区域 */
.main-content {
    margin-left: 280px;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed + .main-content {
    margin-left: 70px;
}

/* 顶部导航栏 */
.top-header {
    background: white;
    padding: 0 30px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: #f8f9fa;
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 15px;
    color: #999;
}

.search-box input {
    padding: 10px 15px 10px 40px;
    border: 2px solid #e1e8ed;
    border-radius: 25px;
    width: 300px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.header-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #e74c3c;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

/* 页面容器 */
.page-container {
    flex: 1;
    padding: 30px;
}

.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

/* 数据大屏样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-info h3 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-change {
    font-size: 12px;
    font-weight: 600;
}

.stat-change.positive {
    color: #27ae60;
}

.stat-change.negative {
    color: #e74c3c;
}

/* 图表网格 */
.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.chart-header {
    padding: 20px 25px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.chart-actions {
    display: flex;
    gap: 5px;
}

.chart-btn {
    padding: 6px 12px;
    border: 1px solid #e1e8ed;
    background: white;
    color: #666;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.chart-btn.active,
.chart-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.chart-container {
    padding: 20px 25px;
    height: 300px;
}

/* 活动区域 */
.activity-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.activity-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    padding: 25px;
}

.activity-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.activity-content p {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 5px;
}

.activity-time {
    font-size: 12px;
    color: #999;
}

/* 题库管理页面样式 */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
}

.page-header h2 {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
}

.primary-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* 筛选区域 */
.filters-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

.filter-select {
    padding: 10px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
    min-width: 150px;
}

.filter-select:focus {
    outline: none;
    border-color: #3498db;
}

.search-group {
    display: flex;
    gap: 10px;
    margin-left: auto;
}

.search-input {
    padding: 10px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    width: 250px;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3498db;
}

.search-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-btn:hover {
    background: #2980b9;
}

/* 表格样式 */
.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 20px;
}

.questions-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.questions-table th:nth-child(1) { width: 50px; }   /* ID列 */
.questions-table th:nth-child(2) { width: 70px; }   /* 类型列 */
.questions-table th:nth-child(3) { width: 25%; }    /* 题目内容列 */
.questions-table th:nth-child(4) { width: 80px; }   /* 用户图片列 */
.questions-table th:nth-child(5) { width: 80px; }   /* 题干图片列 */
.questions-table th:nth-child(6) { width: 20%; }    /* 选项列 */
.questions-table th:nth-child(7) { width: 100px; }  /* 正确答案列 */
.questions-table th:nth-child(8) { width: 80px; }   /* 是否验证列 */
.questions-table th:nth-child(9) { width: 90px; }   /* 创建时间列 */
.questions-table th:nth-child(10) { width: 120px; } /* 操作列 */

.questions-table th {
    background: #f8f9fa;
    padding: 15px 20px;
    text-align: left;
    font-weight: 600;
    color: #555;
    border-bottom: 1px solid #e1e8ed;
}

.questions-table td {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.questions-table tbody tr:hover {
    background: #f8f9fa;
}

.question-content {
    max-width: 400px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    cursor: pointer;
    transition: all 0.3s ease;
}

.question-content:hover {
    background: #f8f9fa;
    padding: 8px;
    border-radius: 6px;
    max-height: none;
    -webkit-line-clamp: unset;
    overflow: visible;
    position: relative;
    z-index: 10;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 图片列样式 */
.user-image-cell {
    text-align: center;
}

.user-image-small {
    width: 50px;
    height: 35px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e1e8ed;
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-image-small:hover {
    transform: scale(1.05);
    border-color: #3498db;
}

.no-user-image {
    width: 50px;
    height: 35px;
    /* 完全空白，不显示任何内容 */
}

.question-image-cell {
    text-align: center;
}

.question-image-small {
    width: 50px;
    height: 35px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e1e8ed;
    cursor: pointer;
    transition: all 0.3s ease;
}

.question-image-small:hover {
    transform: scale(1.05);
    border-color: #3498db;
}

.no-image {
    width: 50px;
    height: 35px;
    border-radius: 4px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 10px;
    border: 1px solid #e1e8ed;
    margin: 0 auto;
}

/* 选项列样式 */
.options-content {
    max-width: 200px;
    line-height: 1.4;
    font-size: 13px;
    height: calc(1.4em * 4 + 6px); /* 严格控制4行高度，增加间距 */
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option-item {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
    color: #555;
    line-height: 1.4;
    height: 1.4em;
}

.options-content:hover {
    background: #f8f9fa;
    padding: 6px;
    border-radius: 4px;
    max-height: none;
    -webkit-line-clamp: unset;
    overflow: visible;
    position: relative;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}



/* 正确答案列样式 */
.correct-answer {
    font-weight: 600;
    color: #27ae60;
    background: #e8f5e8;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
    text-align: center;
}

/* 验证状态列样式 */
.verification-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    display: inline-block;
    min-width: 60px;
}

.verification-verified {
    background: #e8f5e8;
    color: #27ae60;
}

.verification-pending {
    background: #fff3e0;
    color: #f57c00;
}

.verification-rejected {
    background: #ffebee;
    color: #e74c3c;
}

.type-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    display: inline-block;
}

.type-single {
    background: #e3f2fd;
    color: #1976d2;
}

.type-multiple {
    background: #f3e5f5;
    color: #7b1fa2;
}

.type-judge {
    background: #e8f5e8;
    color: #388e3c;
}

.type-fill {
    background: #fff3e0;
    color: #f57c00;
}

.difficulty-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.difficulty-easy {
    background: #e8f5e8;
    color: #4caf50;
}

.difficulty-medium {
    background: #fff3e0;
    color: #ff9800;
}

.difficulty-hard {
    background: #ffebee;
    color: #f44336;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn-small {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #3498db;
    color: white;
}

.btn-edit:hover {
    background: #2980b9;
}

.btn-delete {
    background: #e74c3c;
    color: white;
}

.btn-delete:hover {
    background: #c0392b;
}

.btn-preview {
    background: #95a5a6;
    color: white;
}

.btn-preview:hover {
    background: #7f8c8d;
}

/* 分页样式 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    padding: 20px;
}

.page-btn {
    padding: 8px 16px;
    border: 2px solid #e1e8ed;
    background: white;
    color: #666;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    font-size: 14px;
    color: #666;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .search-box input {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .page-container {
        padding: 20px 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .top-header {
        padding: 0 15px;
    }

    .search-box {
        display: none;
    }

    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-group {
        margin-left: 0;
    }

    .table-container {
        overflow-x: auto;
    }

    .questions-table {
        min-width: 1200px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }

    .action-btn-small {
        padding: 4px 8px;
        font-size: 11px;
    }
}
