* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    width: 100%;
    max-width: 400px;
    position: relative;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.logo i {
    font-size: 24px;
    color: white;
}

.login-header h1 {
    color: #333;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
}

.login-header p {
    color: #666;
    font-size: 14px;
}

.login-form {
    margin-bottom: 20px;
}

.input-group {
    margin-bottom: 20px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 0 15px;
    transition: all 0.3s ease;
}

.input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-wrapper i {
    color: #999;
    margin-right: 12px;
    font-size: 16px;
}

.input-wrapper input {
    flex: 1;
    border: none;
    outline: none;
    padding: 16px 0;
    font-size: 16px;
    color: #333;
}

.input-wrapper input::placeholder {
    color: #999;
}

.verification-wrapper {
    padding-right: 0;
    padding-left: 15px;
}

.verification-wrapper input {
    border-radius: 0;
}

.get-code-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 16px 20px;
    border-radius: 0 10px 10px 0;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 120px;
    flex-shrink: 0;
    height: 100%;
    border-left: 1px solid #e1e5e9;
}

.get-code-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a6fd8, #6a42a0);
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.get-code-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    border-left: 1px solid #ddd;
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 16px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.login-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.login-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.error-message {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
    margin-left: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.error-message.show {
    opacity: 1;
}

.login-footer {
    text-align: center;
}

.login-footer p {
    color: #999;
    font-size: 12px;
}

.success-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #27ae60;
    color: white;
    padding: 20px 30px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.success-message.show {
    opacity: 1;
    visibility: visible;
}

.success-message i {
    font-size: 20px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    .login-card {
        padding: 30px 20px;
        border-radius: 15px;
    }
    
    .verification-wrapper {
        flex-direction: column;
        align-items: stretch;
        padding: 15px;
    }

    .verification-wrapper input {
        margin-bottom: 10px;
        border-radius: 12px 12px 0 0;
        border-bottom: none;
    }

    .get-code-btn {
        margin-left: 0;
        width: 100%;
        min-width: auto;
        border-radius: 0 0 12px 12px;
        border-left: none;
        border-top: 1px solid #e1e5e9;
    }
}
