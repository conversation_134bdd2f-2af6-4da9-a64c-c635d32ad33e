// DOM 元素
const phoneInput = document.getElementById('phoneNumber');
const codeInput = document.getElementById('verificationCode');
const getCodeBtn = document.getElementById('getCodeBtn');
const loginBtn = document.getElementById('loginBtn');
const loginForm = document.getElementById('loginForm');
const phoneError = document.getElementById('phoneError');
const codeError = document.getElementById('codeError');
const successMessage = document.getElementById('successMessage');

// 状态变量
let countdown = 0;
let countdownTimer = null;

// 手机号验证正则
const phoneRegex = /^1[3-9]\d{9}$/;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 手机号输入监听
    phoneInput.addEventListener('input', function() {
        const phone = this.value.replace(/\D/g, ''); // 只保留数字
        this.value = phone;
        validatePhone();
    });

    // 验证码输入监听
    codeInput.addEventListener('input', function() {
        const code = this.value.replace(/\D/g, ''); // 只保留数字
        this.value = code;
        validateForm();
    });

    // 获取验证码按钮点击
    getCodeBtn.addEventListener('click', getVerificationCode);

    // 表单提交
    loginForm.addEventListener('submit', handleLogin);
});

// 验证手机号
function validatePhone() {
    const phone = phoneInput.value;
    const isValid = phoneRegex.test(phone);
    
    if (phone && !isValid) {
        showError(phoneError, '请输入正确的手机号码');
        getCodeBtn.disabled = true;
    } else {
        hideError(phoneError);
        getCodeBtn.disabled = !isValid || countdown > 0;
    }
    
    validateForm();
    return isValid;
}

// 验证表单
function validateForm() {
    const phoneValid = phoneRegex.test(phoneInput.value);
    const codeValid = codeInput.value.length === 6;
    
    loginBtn.disabled = !(phoneValid && codeValid);
}

// 显示错误信息
function showError(element, message) {
    element.textContent = message;
    element.classList.add('show');
}

// 隐藏错误信息
function hideError(element) {
    element.classList.remove('show');
}

// 获取验证码
function getVerificationCode() {
    if (!validatePhone()) {
        return;
    }
    
    const phone = phoneInput.value;
    
    // 模拟发送验证码
    console.log(`向手机号 ${phone} 发送验证码`);
    
    // 这里可以添加实际的API调用
    // sendSMSCode(phone);
    
    // 开始倒计时
    startCountdown();
    
    // 显示成功提示
    showToast('验证码已发送，请注意查收');
}

// 开始倒计时
function startCountdown() {
    countdown = 60;
    getCodeBtn.disabled = true;
    updateCountdownText();
    
    countdownTimer = setInterval(() => {
        countdown--;
        updateCountdownText();
        
        if (countdown <= 0) {
            clearInterval(countdownTimer);
            getCodeBtn.disabled = false;
            getCodeBtn.textContent = '获取验证码';
        }
    }, 1000);
}

// 更新倒计时文本
function updateCountdownText() {
    if (countdown > 0) {
        getCodeBtn.textContent = `${countdown}s后重新获取`;
    }
}

// 处理登录
function handleLogin(e) {
    e.preventDefault();
    
    const phone = phoneInput.value;
    const code = codeInput.value;
    
    // 验证手机号
    if (!phoneRegex.test(phone)) {
        showError(phoneError, '请输入正确的手机号码');
        return;
    }
    
    // 验证验证码
    if (code.length !== 6) {
        showError(codeError, '请输入6位验证码');
        return;
    }
    
    // 模拟登录验证
    loginBtn.disabled = true;
    loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';
    
    // 模拟API调用延迟
    setTimeout(() => {
        // 这里可以添加实际的登录API调用
        // verifyCodeAndLogin(phone, code);
        
        // 模拟登录成功
        if (code === '123456') { // 模拟正确的验证码
            showSuccessMessage();
            console.log(`用户 ${phone} 登录成功`);
            
            // 3秒后可以跳转到主页
            setTimeout(() => {
                // window.location.href = '/dashboard';
                console.log('跳转到主页');
            }, 3000);
        } else {
            showError(codeError, '验证码错误，请重新输入');
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录';
        }
    }, 1500);
}

// 显示成功消息
function showSuccessMessage() {
    successMessage.classList.add('show');
    
    setTimeout(() => {
        successMessage.classList.remove('show');
    }, 3000);
}

// 显示提示消息
function showToast(message) {
    // 创建临时提示元素
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        z-index: 1001;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 100);
    
    // 3秒后移除
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// 实际项目中的API调用示例（注释掉的代码）
/*
async function sendSMSCode(phone) {
    try {
        const response = await fetch('/api/send-sms', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ phone })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showToast('验证码已发送');
            startCountdown();
        } else {
            showError(phoneError, result.message || '发送失败，请重试');
        }
    } catch (error) {
        showError(phoneError, '网络错误，请重试');
    }
}

async function verifyCodeAndLogin(phone, code) {
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ phone, code })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccessMessage();
            // 保存token
            localStorage.setItem('token', result.token);
            // 跳转到主页
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 2000);
        } else {
            showError(codeError, result.message || '登录失败');
        }
    } catch (error) {
        showError(codeError, '网络错误，请重试');
    } finally {
        loginBtn.disabled = false;
        loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录';
    }
}
*/
